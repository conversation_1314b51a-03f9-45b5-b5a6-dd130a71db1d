3d6 execution was cancelled!
2025-07-16 15:47:41 - <PERSON>Manager - INFO - Workflow terminated flag set to: True
2025-07-16 15:47:41 - Kafka<PERSON>orkflowConsumer - WARNING - Workflow execution for 'b33ceffe-e932-4e14-bdd1-fb9aeebccbb0' was cancelled
2025-07-16 15:47:41 - KafkaWorkflowConsumer - INFO - Workflow 'b33ceffe-e932-4e14-bdd1-fb9aeebccbb0' final status: cancelled, result: Workflow 'b33ceffe-e932-4e14-bdd1-fb9aeebccbb0' execution was cancelled
2025-07-16 15:47:41 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6, response: {'status': 'Workflow Cancelled', 'result': "Workflow 'b33ceffe-e932-4e14-bdd1-fb9aeebccbb0' execution was cancelled", 'workflow_status': 'cancelled'}
2025-07-16 15:47:41 - Ka<PERSON><PERSON><PERSON>orkflowConsumer - DEBUG - Stopped workflow with correlation_id: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6 
2025-07-16 15:47:41 - KafkaWorkflowConsumer - INFO - Committed offset after processing approval-request: 281, corr_id: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6
2025-07-16 15:47:48 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-16 15:47:48 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:47:48 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:47:48 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
^C2025-07-16 15:48:07 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
^C2025-07-16 15:48:07 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-07-16 15:48:07 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-07-16 15:48:07 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-07-16 15:48:07 - Main - ERROR - Shutting down due to keyboard interrupt...
prathamagarwal@Pratham-ka-MacBook-Air orchestration-engine % 
prathamagarwal@Pratham-ka-MacBook-Air orchestration-engine % clear
prathamagarwal@Pratham-ka-MacBook-Air orchestration-engine % ./run_local.sh
Installing dependencies...
Installing dependencies from lock file

No dependencies to install or update
Starting Orchestration Engine
2025-07-16 15:48:13 - Main - INFO - Starting Server
2025-07-16 15:48:13 - Main - INFO - Connection at: **************:9092
2025-07-16 15:48:13 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-16 15:48:13 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-16 15:48:13 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-16 15:48:13 - WorkflowExecutor - INFO - WorkflowExecutor initialized.
2025-07-16 15:48:13 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-16 15:48:13 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-16 15:48:14 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-16 15:48:14 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-16 15:48:16 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-16 15:48:18 - PostgresManager - INFO - PostgreSQL connection pool created
2025-07-16 15:48:18 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-07-16 15:48:20 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-16 15:48:21 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-07-16 15:48:21 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-16 15:48:23 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-16 15:48:23 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-16 15:48:24 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-16 15:48:24 - RedisEventListener - INFO - Starting Redis event listener thread
2025-07-16 15:48:24 - RedisEventListener - INFO - Redis event listener started
2025-07-16 15:48:24 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-07-16 15:48:24 - StateManager - DEBUG - Using provided database connections
2025-07-16 15:48:24 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-16 15:48:24 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-16 15:48:24 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-16 15:48:25 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-07-16 15:48:25 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-16 15:48:25 - StateManager - INFO - WorkflowStateManager initialized
2025-07-16 15:48:25 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-07-16 15:48:25 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-07-16 15:48:26 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-07-16 15:48:26 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-07-16 15:48:28 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-07-16 15:48:28 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-07-16 15:48:28 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-07-16 15:48:39 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-07-16 15:48:45 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-07-16 15:48:45 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-07-16 15:48:45 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-07-16 15:48:51 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-07-16 15:48:51 - NodeExecutor - INFO - Background result consumer loop started.
2025-07-16 15:48:51 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-07-16 15:48:58 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-07-16 15:48:58 - AgentExecutor - INFO - Background result consumer loop started.
2025-07-16 15:48:58 - WorkflowExecutor - INFO - WorkflowExecutor started successfully.
2025-07-16 15:48:58 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1369
2025-07-16 15:48:58 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1752661111, 'task_type': 'workflow', 'data': {'workflow_id': 'b33ceffe-e932-4e14-bdd1-fb9aeebccbb0', 'payload': {'user_dependent_fields': ['main_input'], 'user_payload_template': {'main_input': {'value': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'transition_id': 'MergeDataComponent-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-07-16 15:48:58 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: b33ceffe-e932-4e14-bdd1-fb9aeebccbb0
2025-07-16 15:48:58 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/b33ceffe-e932-4e14-bdd1-fb9aeebccbb0
2025-07-16 15:49:00 - WorkflowService - DEBUG - Received response with status code: 200
2025-07-16 15:49:00 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow testing_migration retrieved successfully",
  "workflow": {
    "id": "b33ceffe-e932-4e14-bdd1-fb9aeebccbb0",
    "name": "testing_migration",
    "description": "testing_migration",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/b12d9596-c1bc-4e52-b036-4106a02a9eca.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/51a32dc1-1e05-42be-86f3-e8b3cee909a0.json",
    "start_nodes": [
      {
        "field": "main_input",
        "type": "dict",
        "transition_id": "transition-MergeDataComponent-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-07-16T06:00:21.689664",
    "updated_at": "2025-07-16T10:10:53.551079",
    "available_nodes": [
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************",
        "label": "Merge Data"
      },
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-*************",
        "label": "AI Agent Executor"
      }
    ],
    "is_updated": true,
    "source_version_id": null
  }
}
2025-07-16 15:49:01 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for b33ceffe-e932-4e14-bdd1-fb9aeebccbb0 - server_script_path is optional
2025-07-16 15:49:01 - app.services.initialize_workflow - DEBUG - Skipping field 'main_input' for transition 'transition-MergeDataComponent-*************' (intended for 'MergeDataComponent-*************')
2025-07-16 15:49:01 - app.services.initialize_workflow - DEBUG - Processing user-dependent field 'main_input' for transition 'transition-MergeDataComponent-*************'
2025-07-16 15:49:01 - app.services.initialize_workflow - DEBUG - Target transition for field 'main_input': 'MergeDataComponent-*************'
2025-07-16 15:49:01 - app.services.initialize_workflow - DEBUG - Target transition 'MergeDataComponent-*************' doesn't exist, using as fallback for 'transition-MergeDataComponent-*************'
2025-07-16 15:49:01 - app.services.initialize_workflow - DEBUG - Calling _inject_user_input_for_conditional_components for transition-MergeDataComponent-*************
2025-07-16 15:49:01 - app.services.initialize_workflow - DEBUG - Checking transition transition-MergeDataComponent-*************: node_type='', is_conditional=False
2025-07-16 15:49:01 - app.services.initialize_workflow - DEBUG - Skipping non-conditional transition transition-MergeDataComponent-*************
2025-07-16 15:49:01 - app.services.initialize_workflow - DEBUG - Calling _inject_user_input_for_conditional_components for transition-AgenticAI-*************
2025-07-16 15:49:01 - app.services.initialize_workflow - DEBUG - Checking transition transition-AgenticAI-*************: node_type='', is_conditional=False
2025-07-16 15:49:01 - app.services.initialize_workflow - DEBUG - Skipping non-conditional transition transition-AgenticAI-*************
2025-07-16 15:49:01 - app.services.initialize_workflow - DEBUG - Preserved payload structure in workflow: {'user_dependent_fields': ['main_input'], 'user_payload_template': {'main_input': {'value': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'transition_id': 'MergeDataComponent-*************'}}}
2025-07-16 15:49:01 - EnhancedWorkflowEngine - DEBUG - Stored user_payload_template: {'main_input': {'value': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'transition_id': 'MergeDataComponent-*************'}}
2025-07-16 15:49:01 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-07-16 15:49:01 - StateManager - DEBUG - Using global database connections from initializer
2025-07-16 15:49:01 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-16 15:49:01 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-16 15:49:01 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-16 15:49:02 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-16 15:49:02 - StateManager - INFO - WorkflowStateManager initialized
2025-07-16 15:49:02 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-07-16 15:49:02 - StateManager - DEBUG - Using provided database connections
2025-07-16 15:49:02 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-16 15:49:02 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-16 15:49:02 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-16 15:49:03 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-16 15:49:03 - StateManager - INFO - WorkflowStateManager initialized
2025-07-16 15:49:03 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-MergeDataComponent-*************']
2025-07-16 15:49:03 - StateManager - INFO - Built dependency map for 2 transitions
2025-07-16 15:49:03 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-MergeDataComponent-*************']
2025-07-16 15:49:03 - EnhancedWorkflowEngine - DEBUG - Found end transition: transition-AgenticAI-*************
2025-07-16 15:49:03 - EnhancedWorkflowEngine - INFO - Found 1 end transitions: {'transition-AgenticAI-*************'}
2025-07-16 15:49:03 - StateManager - INFO - Set end transitions: {'transition-AgenticAI-*************'}
2025-07-16 15:49:03 - MCPToolExecutor - DEBUG - Set correlation ID to: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6
2025-07-16 15:49:03 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 5dff9da3-71f8-4559-a062-9d57ffbc4ce6 in tool_executor
2025-07-16 15:49:03 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-07-16 15:49:03 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-07-16 15:49:03 - NodeExecutor - DEBUG - Set correlation ID to: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6
2025-07-16 15:49:03 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 5dff9da3-71f8-4559-a062-9d57ffbc4ce6 in node_executor
2025-07-16 15:49:03 - AgentExecutor - DEBUG - Set correlation ID to: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6
2025-07-16 15:49:03 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 5dff9da3-71f8-4559-a062-9d57ffbc4ce6 in agent_executor
2025-07-16 15:49:03 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-07-16 15:49:03 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-07-16 15:49:03 - TransitionHandler - INFO - TransitionHandler initialized
2025-07-16 15:49:03 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6
2025-07-16 15:49:03 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6
2025-07-16 15:49:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6, response: {'workflow_id': 'b33ceffe-e932-4e14-bdd1-fb9aeebccbb0', 'status': 'Initialized', 'message': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-07-16 15:49:03 - StateManager - INFO - Workflow initialized with initial transition: transition-MergeDataComponent-*************
2025-07-16 15:49:03 - StateManager - DEBUG - State: pending={'transition-MergeDataComponent-*************'}, waiting=set(), completed=set()
2025-07-16 15:49:03 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-MergeDataComponent-*************
2025-07-16 15:49:03 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-07-16 15:49:03 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:5dff9da3-71f8-4559-a062-9d57ffbc4ce6'
2025-07-16 15:49:04 - RedisManager - DEBUG - Set key 'workflow_state:5dff9da3-71f8-4559-a062-9d57ffbc4ce6' with TTL of 600 seconds
2025-07-16 15:49:04 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6. Will be archived to PostgreSQL when Redis key expires.
2025-07-16 15:49:04 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-16 15:49:04 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-16 15:49:04 - StateManager - INFO - Cleared 1 pending transitions: {'transition-MergeDataComponent-*************'}
2025-07-16 15:49:04 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-16 15:49:04 - StateManager - INFO - Terminated: False
2025-07-16 15:49:04 - StateManager - INFO - Pending transitions (0): []
2025-07-16 15:49:04 - StateManager - INFO - Waiting transitions (0): []
2025-07-16 15:49:04 - StateManager - INFO - Completed transitions (0): []
2025-07-16 15:49:04 - StateManager - INFO - Results stored for 0 transitions
2025-07-16 15:49:04 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 15:49:04 - StateManager - INFO - Workflow status: inactive
2025-07-16 15:49:04 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 15:49:04 - StateManager - INFO - Workflow status: inactive
2025-07-16 15:49:04 - StateManager - INFO - Workflow paused: False
2025-07-16 15:49:04 - StateManager - INFO - ==============================
2025-07-16 15:49:04 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-07-16 15:49:04 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 5dff9da3-71f8-4559-a062-9d57ffbc4ce6):
2025-07-16 15:49:04 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-07-16 15:49:04 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=initial, execution_type=Components)
2025-07-16 15:49:04 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-16 15:49:04 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-07-16 15:49:04 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-16 15:49:04 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-16 15:49:04 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=component, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-16 15:49:04 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-07-16 15:49:04 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-07-16 15:49:04 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 3 fields (21 null/empty fields removed)
2025-07-16 15:49:04 - TransitionHandler - DEBUG - tool Parameters: {'main_input': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'num_additional_inputs': 0, 'merge_strategy': 'Overwrite'}
2025-07-16 15:49:04 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'num_additional_inputs': 0, 'merge_strategy': 'Overwrite'}
2025-07-16 15:49:04 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 5dff9da3-71f8-4559-a062-9d57ffbc4ce6):
2025-07-16 15:49:04 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server Merge Data', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-07-16 15:49:04 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: a9cb5cd4-e911-4baa-b5e5-fa03be3937e4) using provided producer.
2025-07-16 15:49:04 - NodeExecutor - DEBUG - Added correlation_id 5dff9da3-71f8-4559-a062-9d57ffbc4ce6 to payload
2025-07-16 15:49:04 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-07-16 15:49:04 - NodeExecutor - DEBUG - Added node_label Merge Data to payload
2025-07-16 15:49:04 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'num_additional_inputs': 0, 'merge_strategy': 'Overwrite'}, 'request_id': 'a9cb5cd4-e911-4baa-b5e5-fa03be3937e4', 'correlation_id': '5dff9da3-71f8-4559-a062-9d57ffbc4ce6', 'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data'}
2025-07-16 15:49:04 - NodeExecutor - DEBUG - Request a9cb5cd4-e911-4baa-b5e5-fa03be3937e4 sent successfully using provided producer.
2025-07-16 15:49:04 - NodeExecutor - DEBUG - Waiting indefinitely for result for request a9cb5cd4-e911-4baa-b5e5-fa03be3937e4...
2025-07-16 15:49:04 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1369, corr_id: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6
2025-07-16 15:49:05 - NodeExecutor - DEBUG - Result consumer received message: Offset=1416
2025-07-16 15:49:05 - NodeExecutor - DEBUG - Received valid result for request_id a9cb5cd4-e911-4baa-b5e5-fa03be3937e4
2025-07-16 15:49:05 - NodeExecutor - INFO - Result received for request a9cb5cd4-e911-4baa-b5e5-fa03be3937e4.
2025-07-16 15:49:05 - TransitionHandler - INFO - Execution result from Components executor: {
  "email_1": "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"
}
2025-07-16 15:49:05 - TransitionHandler - INFO - Checking execution result for errors: {
  "email_1": "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"
}
2025-07-16 15:49:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 5dff9da3-71f8-4559-a062-9d57ffbc4ce6):
2025-07-16 15:49:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'raw_result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'approval_required': False}
2025-07-16 15:49:05 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'result': {'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}}, 'status': 'completed', 'timestamp': 1752661145.0995321}}
2025-07-16 15:49:05 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-07-16 15:49:06 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-07-16 15:49:06 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-16 15:49:06 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-07-16 15:49:06 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-MergeDataComponent-*************'}
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'dict'>
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔀 Execution result keys: ['email_1']
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MergeDataComponent-*************
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************']
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-AgenticAI-*************']
2025-07-16 15:49:06 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 2.12 seconds
2025-07-16 15:49:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 5dff9da3-71f8-4559-a062-9d57ffbc4ce6):
2025-07-16 15:49:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6, response: {'result': 'Completed transition in 2.12 seconds', 'message': 'Transition completed in 2.12 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MergeDataComponent-*************: ['transition-AgenticAI-*************']
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-16 15:49:06 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-AgenticAI-*************']]
2025-07-16 15:49:06 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-MergeDataComponent-*************: ['transition-AgenticAI-*************']
2025-07-16 15:49:06 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-16 15:49:06 - EnhancedWorkflowEngine - INFO - Transition transition-MergeDataComponent-************* completed successfully: 1 next transitions
2025-07-16 15:49:06 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-AgenticAI-*************']
2025-07-16 15:49:06 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-AgenticAI-*************']
2025-07-16 15:49:06 - EnhancedWorkflowEngine - INFO - Adding transition transition-AgenticAI-************* to pending (all dependencies met)
2025-07-16 15:49:06 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-16 15:49:06 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:5dff9da3-71f8-4559-a062-9d57ffbc4ce6'
2025-07-16 15:49:06 - RedisManager - DEBUG - Set key 'workflow_state:5dff9da3-71f8-4559-a062-9d57ffbc4ce6' with TTL of 600 seconds
2025-07-16 15:49:06 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6. Will be archived to PostgreSQL when Redis key expires.
2025-07-16 15:49:06 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-16 15:49:06 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-16 15:49:06 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-16 15:49:06 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-16 15:49:06 - StateManager - INFO - Terminated: False
2025-07-16 15:49:06 - StateManager - INFO - Pending transitions (0): []
2025-07-16 15:49:06 - StateManager - INFO - Waiting transitions (0): []
2025-07-16 15:49:06 - StateManager - INFO - Completed transitions (1): ['transition-MergeDataComponent-*************']
2025-07-16 15:49:06 - StateManager - INFO - Results stored for 1 transitions
2025-07-16 15:49:06 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 15:49:06 - StateManager - INFO - Workflow status: inactive
2025-07-16 15:49:06 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 15:49:06 - StateManager - INFO - Workflow status: inactive
2025-07-16 15:49:06 - StateManager - INFO - Workflow paused: False
2025-07-16 15:49:06 - StateManager - INFO - ==============================
2025-07-16 15:49:06 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-16 15:49:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 5dff9da3-71f8-4559-a062-9d57ffbc4ce6):
2025-07-16 15:49:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-07-16 15:49:06 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=standard, execution_type=agent)
2025-07-16 15:49:06 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-16 15:49:06 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=agent, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-16 15:49:07 - StateManager - DEBUG - Retrieved result for transition transition-MergeDataComponent-************* from Redis
2025-07-16 15:49:07 - StateManager - DEBUG - Detected wrapped result structure for transition transition-MergeDataComponent-*************, extracting data
2025-07-16 15:49:07 - StateManager - DEBUG - Extracted double-nested result data for transition transition-MergeDataComponent-*************
2025-07-16 15:49:07 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-MergeDataComponent-*************
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-MergeDataComponent-************* (total: 1)
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'output_data': {'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}}
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle output_data: {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - Found result.result: {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"} (type: <class 'dict'>)
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - Handle 'output_data' not found in dict keys: ['email_1']
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - No handle matches found for 'output_data', treating result as single-value
2025-07-16 15:49:07 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-16 15:49:07 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Looking for results for transition transition-MergeDataComponent-*************, iteration_context: False
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Base ID transition-MergeDataComponent-************* results: found
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'output_data': {'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}}
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle output_data: {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - Found result.result: {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"} (type: <class 'dict'>)
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - Handle 'output_data' not found in dict keys: ['email_1']
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - No handle matches found for 'output_data', treating result as single-value
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - ✅ Handle mapping success: output_data → input_variables via path 'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}
2025-07-16 15:49:07 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-16 15:49:07 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - 🔧 Preserving field 'query' with agent-platform template variables: You are an email scoring specialist. Analyze multiple emails and return the best one based on scorin...
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - Filtering out field 'input' with null/empty value: 
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with null/empty value: None
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - Filtering out field 'description' with null/empty value: 
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-16 15:49:07 - WorkflowUtils - INFO - 🧹 Parameter filtering: 8 → 6 fields (2 null/empty fields removed)
2025-07-16 15:49:07 - WorkflowUtils - DEBUG - 🔧 Query field preserved in filtered_result: You are an email scoring specialist. Analyze multiple emails and return the best one based on scorin...
2025-07-16 15:49:07 - TransitionHandler - DEBUG - 📌 Using top-level system_message: Analyze multiple emails and return the best one based on scoring criteria
2025-07-16 15:49:07 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_type = component
2025-07-16 15:49:07 - TransitionHandler - DEBUG - 📌 Added static parameter: execution_type = response
2025-07-16 15:49:07 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_config = {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}
2025-07-16 15:49:07 - TransitionHandler - DEBUG - 📌 Added static parameter: variables = {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}
2025-07-16 15:49:07 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_variables': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}, 'variables': {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}}
2025-07-16 15:49:07 - TransitionHandler - DEBUG - tool Parameters: {'input_variables': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}, 'variables': {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}}
2025-07-16 15:49:07 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'input_variables': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}, 'variables': {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}}
2025-07-16 15:49:07 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 5dff9da3-71f8-4559-a062-9d57ffbc4ce6):
2025-07-16 15:49:07 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6, response: {'transition_id': 'transition-AgenticAI-*************', 'node_label': 'AI Agent Executor', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AI Agent Executor', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-07-16 15:49:07 - AgentExecutor - DEBUG - Component agent: query='None', input='None', final_query='None'
2025-07-16 15:49:07 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 696b34f7-55d6-4a06-bfc8-3d9d531c649d) with correlation_id: 5dff9da3-71f8-4559-a062-9d57ffbc4ce6, user_id: c1454e90-09ac-40f2-bde2-833387d7b645 using provided producer.
2025-07-16 15:49:07 - AgentExecutor - INFO - Building component agent request for execution_type: response
agent config in build component agent request:  {'id': '14c0f883-a06e-4f67-a2b6-9c03c3571c5a', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}
system_message in build component agent request:  Analyze multiple emails and return the best one based on scoring criteria
input_variables in build component agent request:  {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}
2025-07-16 15:49:07 - AgentExecutor - DEBUG - Added correlation_id 5dff9da3-71f8-4559-a062-9d57ffbc4ce6 to payload
2025-07-16 15:49:07 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '696b34f7-55d6-4a06-bfc8-3d9d531c649d', 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645', 'correlation_id': '5dff9da3-71f8-4559-a062-9d57ffbc4ce6', 'agent_type': 'component', 'execution_type': 'response', 'query': None, 'variables': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'agent_config': {'id': '14c0f883-a06e-4f67-a2b6-9c03c3571c5a', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-16 15:49:07 - AgentExecutor - DEBUG - Request 696b34f7-55d6-4a06-bfc8-3d9d531c649d sent successfully using provided producer.
2025-07-16 15:49:07 - AgentExecutor - DEBUG - Waiting for single response result for request 696b34f7-55d6-4a06-bfc8-3d9d531c649d...
