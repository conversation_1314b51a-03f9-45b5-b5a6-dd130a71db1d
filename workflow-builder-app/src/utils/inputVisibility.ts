import { InputDefinition, WorkflowNodeData } from "@/types";
import { Node } from "reactflow";
import { shouldShowInput } from "@/utils/visibility-rules";

// Memoization cache for input visibility checks
const visibilityCache = new Map<string, { result: boolean; timestamp: number }>();
const CACHE_TTL = 1000; // 1 second cache TTL

/**
 * Creates a cache key for input visibility checks
 */
function createVisibilityCacheKey(
  inputName: string,
  nodeId: string,
  nodeType: string,
  config: Record<string, any>
): string {
  // Create a stable key based on relevant properties
  const configKey = JSON.stringify(config, Object.keys(config).sort());
  return `${nodeId}:${nodeType}:${inputName}:${configKey}`;
}

/**
 * Determines if an input should be visible based on node type and configuration
 * @param inputDef The input definition
 * @param node The workflow node
 * @param config The current configuration
 * @returns True if the input should be visible, false otherwise
 */
export function checkInputVisibility(
  inputDef: InputDefinition,
  node: Node<WorkflowNodeData> | null,
  config: Record<string, any>
): boolean {
  if (!node) return false;

  // Create cache key for memoization
  const cacheKey = createVisibilityCacheKey(
    inputDef.name,
    node.id,
    node.data.type || node.data.originalType || '',
    config
  );

  // Check cache first
  const cached = visibilityCache.get(cacheKey);
  const now = Date.now();
  if (cached && (now - cached.timestamp) < CACHE_TTL) {
    // Add unique debug log to verify caching is working
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ [CACHE-HIT] ${inputDef.name} - OPTIMIZED VERSION WORKING`);
    }
    return cached.result;
  }

  // Optimized DEBUG: Only log when cache miss occurs and in development mode
  const shouldDebugLog = process.env.NODE_ENV === 'development' &&
    (node.data.type?.includes("MergeData") || node.data.originalType?.includes("MergeData") ||
     node.data.type?.includes("DataCompose") || node.data.originalType?.includes("DataCompose") ||
     inputDef.name.startsWith("output_key_"));

  if (shouldDebugLog && !cached) {
    console.log(`🔍 [VISIBILITY] Checking ${inputDef.name} (cache miss)`, {
      inputName: inputDef.name,
      nodeType: node.data.type,
      originalType: node.data.originalType,
      nodeId: node.id,
      cacheKey: cacheKey.substring(0, 100) + '...', // Truncated for readability
      hasVisibilityRules: !!inputDef.visibility_rules,
      visibilityRulesLength: inputDef.visibility_rules?.length || 0
    });
  }

  // Perform the actual visibility check
  let result: boolean;

  // This debug logging is now handled above in the optimized section

  // Special handling for DynamicCombineTextComponent
  if (
    node.data.type === "DynamicCombineTextComponent" &&
    inputDef.name.startsWith("input_") &&
    !inputDef.is_handle
  ) {
    // Extract the index from the input name (e.g., "input_3" -> 3)
    const match = inputDef.name.match(/input_(\d+)/);
    if (match && match[1]) {
      const inputIndex = parseInt(match[1], 10);
      const numAdditionalInputs = parseInt(config.num_additional_inputs || "0", 10);

      // FIXED: Show inputs for main input (index 1) plus additional inputs
      // Total inputs = 1 (main) + numAdditionalInputs
      // So for numAdditionalInputs=1, show input_1 and input_2
      const totalInputs = 1 + numAdditionalInputs;
      result = inputIndex <= totalInputs;

      if (shouldDebugLog && !cached) {
        console.log(`🔧 [COMBINE-INPUT-DEBUG] ${inputDef.name}: inputIndex=${inputIndex}, numAdditionalInputs=${numAdditionalInputs}, totalInputs=${totalInputs}, shouldShow=${result}`);
      }
    } else {
      result = true;
    }
  }

  // Special handling for MergeDataComponent input fields
  else if (
    (node.data.type === "MergeDataComponent" || node.data.originalType === "MergeDataComponent") &&
    inputDef.name.startsWith("input_") &&
    !inputDef.is_handle
  ) {
    if (shouldDebugLog && !cached) {
      console.log(`🔧 [MERGE-INPUT-DEBUG] Checking input field ${inputDef.name}:`, {
        num_additional_inputs: config.num_additional_inputs
      });
    }

    // Extract the index from the input name (e.g., "input_3" -> 3)
    const match = inputDef.name.match(/input_(\d+)/);
    if (match && match[1]) {
      const inputIndex = parseInt(match[1], 10);
      const numAdditionalInputs = parseInt(config.num_additional_inputs || "0", 10);

      // Show the input if its index is less than or equal to the number of additional inputs
      result = inputIndex <= numAdditionalInputs;
      if (shouldDebugLog && !cached) {
        console.log(`🔧 [MERGE-INPUT-DEBUG] ${inputDef.name}: inputIndex=${inputIndex}, numAdditionalInputs=${numAdditionalInputs}, shouldShow=${result}`);
      }
    } else {
      result = true;
    }
  }

  // Special handling for MergeDataComponent output keys
  if (
    (node.data.type === "MergeDataComponent" || node.data.originalType === "MergeDataComponent") &&
    inputDef.name.startsWith("output_key_")
  ) {
    if (shouldDebugLog && !cached) {
      console.log(`� [OPTIMIZED-MERGE-DEBUG] This should ONLY appear for MergeData components:`, {
        inputName: inputDef.name,
        isMergeDataComponent: true,
        isOutputKey: true,
        nodeType: node.data.type,
        originalType: node.data.originalType,
        willEnterLogic: true,
        UNIQUE_IDENTIFIER: 'OPTIMIZED_VERSION_2025'
      });
    }

    // DEBUG: Log the visibility check with detailed value analysis
    if (shouldDebugLog && !cached) {
      console.log(`🎯 [DEBUG] ENTERING MergeData visibility check for ${inputDef.name}:`, {
        merge_strategy: config.merge_strategy,
        merge_strategy_type: typeof config.merge_strategy,
        merge_strategy_is_undefined: config.merge_strategy === undefined,
        merge_strategy_is_null: config.merge_strategy === null,
        merge_strategy_is_empty: config.merge_strategy === "",
        num_additional_inputs: config.num_additional_inputs,
        config_keys: Object.keys(config || {}),
        config: config
      });
    }

    // Only show output keys when merge_strategy is "Structured Compose"
    const isStructuredCompose = config.merge_strategy === "Structured Compose";
    if (shouldDebugLog && !cached) {
      console.log(`🚨 [CRITICAL-DEBUG] ${inputDef.name}: merge_strategy="${config.merge_strategy}", isStructuredCompose=${isStructuredCompose}`);
    }

    if (!isStructuredCompose) {
      if (shouldDebugLog && !cached) {
        console.log(`✅ [CORRECT-HIDING] ${inputDef.name} hidden: merge_strategy is not "Structured Compose" (actual: "${config.merge_strategy}")`);
      }
      result = false;
    } else {
      // Extract the index from the output key name (e.g., "output_key_3" -> 3)
      const match = inputDef.name.match(/output_key_(\d+)/);
      if (match && match[1]) {
        const keyIndex = parseInt(match[1], 10);
        const numAdditionalInputs = parseInt(config.num_additional_inputs || "0", 10);

        // Show output_key_1 (for main input) and output_key_2 through output_key_(1+numAdditionalInputs)
        // For example: if numAdditionalInputs = 3, show output_key_1, output_key_2, output_key_3, output_key_4
        const totalInputs = 1 + numAdditionalInputs; // main_input + additional inputs
        result = keyIndex <= totalInputs;

        if (shouldDebugLog && !cached) {
          console.log(`[DEBUG] ${inputDef.name}: keyIndex=${keyIndex}, totalInputs=${totalInputs}, shouldShow=${result}`);
        }
      } else {
        // If we can't parse the key index, hide it
        if (shouldDebugLog && !cached) {
          console.log(`[DEBUG] ${inputDef.name} hidden: could not parse key index`);
        }
        result = false;
      }
    }
  }

  // Special handling for DataComposeComponent output keys
  else if (
    (node.data.type === "DataComposeComponent" || node.data.originalType === "DataComposeComponent") &&
    inputDef.name.startsWith("output_key_")
  ) {
    if (shouldDebugLog && !cached) {
      console.log(`🔧 [DATA-COMPOSE-DEBUG] DataComposeComponent output key visibility check:`, {
        inputName: inputDef.name,
        isDataComposeComponent: true,
        isOutputKey: true,
        nodeType: node.data.type,
        originalType: node.data.originalType,
        UNIQUE_IDENTIFIER: 'DATA_COMPOSE_VERSION_2025'
      });
    }

    // For DataComposeComponent, output keys are always visible based on num_additional_inputs
    // Extract the index from the output key name (e.g., "output_key_3" -> 3)
    const match = inputDef.name.match(/output_key_(\d+)/);
    if (match && match[1]) {
      const keyIndex = parseInt(match[1], 10);
      const numAdditionalInputs = parseInt(config.num_additional_inputs || "2", 10);

      // Show output_key_1 (for main input) and output_key_2 through output_key_(1+numAdditionalInputs)
      // For example: if numAdditionalInputs = 3, show output_key_1, output_key_2, output_key_3, output_key_4
      const totalInputs = 1 + numAdditionalInputs; // main_input + additional inputs
      result = keyIndex <= totalInputs;

      if (shouldDebugLog && !cached) {
        console.log(`[DATA-COMPOSE-DEBUG] ${inputDef.name}: keyIndex=${keyIndex}, totalInputs=${totalInputs}, shouldShow=${result}`);
      }
    } else {
      // If we can't parse the key index, hide it
      if (shouldDebugLog && !cached) {
        console.log(`[DATA-COMPOSE-DEBUG] ${inputDef.name} hidden: could not parse key index`);
      }
      result = false;
    }
  }

  // UPDATED: Special handling for ConditionalNode with new dual-purpose naming
  else if (node.data.originalType === "ConditionalNode") {
    // Handle "primary" input (condition 1) - always visible
    if (inputDef.name === "primary") {
      result = true;
    }
    // Handle "condition_X" inputs (conditions 2+) - new dual-purpose naming
    else if (inputDef.name.startsWith("condition_") && inputDef.name.match(/^condition_\d+$/)) {
      const match = inputDef.name.match(/condition_(\d+)/);
      if (match && match[1]) {
        const conditionIndex = parseInt(match[1], 10);
        const numAdditionalConditions = parseInt(config.num_additional_conditions || "0", 10);
        const totalConditions = 1 + numAdditionalConditions; // Base 1 + additional

        // Show if condition index is within total conditions
        result = conditionIndex <= totalConditions;
      } else {
        result = false;
      }
    }
    // Handle dynamic condition configuration inputs (condition_2_*, condition_3_*, etc.)
    else {
      const conditionMatch = inputDef.name.match(/condition_(\d+)_/);
      if (conditionMatch && conditionMatch[1]) {
        const conditionIndex = parseInt(conditionMatch[1], 10);
        const numAdditionalConditions = parseInt(config.num_additional_conditions || "0", 10);
        const totalConditions = 1 + numAdditionalConditions;

        // For conditions 2 and above, check if they should be visible
        if (conditionIndex > 1) {
          // Show if condition index is within total conditions
          result = conditionIndex <= totalConditions;
        } else {
          result = true;
        }
      } else {
        result = true;
      }
    }
  }

  // Special handling for MCP Marketplace components
  else if (isMCPMarketplaceComponent(node)) {
    result = checkMCPMarketplaceInputVisibility(inputDef, node, config);
  }
  // Special handling for MCP Tools component
  else if (node.data.type === "MCPToolsComponent") {
    result = checkMCPToolsInputVisibility(inputDef, config);
  }
  // Default case
  else {
    // CRITICAL: Debug fallback for output_key fields
    if (inputDef.name.startsWith("output_key_") && shouldDebugLog && !cached) {
      console.log(`❌ [FALLBACK-ISSUE] output_key field ${inputDef.name} falling back to generic visibility rules!`, {
        inputName: inputDef.name,
        nodeType: node.data.type,
        originalType: node.data.originalType,
        isMergeDataComponent: node.data.type === "MergeDataComponent" || node.data.originalType === "MergeDataComponent",
        isDataComposeComponent: node.data.type === "DataComposeComponent" || node.data.originalType === "DataComposeComponent",
        merge_strategy: config?.merge_strategy,
        hasVisibilityRules: !!inputDef.visibility_rules,
        visibilityRules: inputDef.visibility_rules,
        willShowByDefault: !inputDef.visibility_rules || inputDef.visibility_rules.length === 0
      });
    }

    // Use the utility function for standard visibility rules
    result = shouldShowInput(inputDef, config);
  }

  // Cache the result
  visibilityCache.set(cacheKey, { result, timestamp: now });

  return result;
}

/**
 * Checks if a node is an MCP Marketplace component
 */
function isMCPMarketplaceComponent(node: Node<WorkflowNodeData> | null): boolean {
  if (!node) return false;
  return node.data.type === "MCPMarketplaceComponent" ||
         (node.data.definition?.category === "MCP Marketplace");
}

/**
 * Checks visibility for MCP Marketplace component inputs
 */
function checkMCPMarketplaceInputVisibility(
  inputDef: InputDefinition,
  node: Node<WorkflowNodeData>,
  config: Record<string, any>
): boolean {
  // For explicit handle inputs (ending with _handle), always show them
  if (inputDef.input_type === "handle" || inputDef.name.endsWith("_handle")) {
    // Always show explicit handle inputs
    return true;
  }

  // Hide connection fields that have a direct input equivalent
  if (inputDef.name.endsWith("_connection")) {
    // Check if there's a direct input with the same base name
    const baseName = inputDef.name.replace("_connection", "");
    const hasDirectInput =
      node.data?.definition?.inputs?.some((input) => input.name === baseName) || false;
    if (hasDirectInput) {
      return false;
    }
  }

  // For inputs with is_handle=true, always show them
  if (inputDef.is_handle) {
    return true;
  }

  // For regular inputs, check if there's a corresponding handle
  if (hasCorrespondingHandle(inputDef.name, node)) {
    // If the handle is connected, hide the direct input
    // This would require the isInputConnected function from useConnectedHandles
    // For now, we'll return true and handle this in the component
    return true;
  }

  // Default to showing the input
  return true;
}

/**
 * Checks if an input has a corresponding handle
 */
function hasCorrespondingHandle(inputName: string, node: Node<WorkflowNodeData>): boolean {
  if (!node?.data?.definition?.inputs) return false;

  // Check for handle with a suffix pattern (e.g., input_dict_handle for input_dict)
  const handleSuffix = node.data.definition.inputs.find(
    (input) =>
      (input.is_handle || input.input_type === "handle") && input.name === `${inputName}_handle`,
  );

  return !!handleSuffix;
}

/**
 * Checks visibility for MCP Tools component inputs
 */
function checkMCPToolsInputVisibility(
  inputDef: InputDefinition,
  config: Record<string, any>
): boolean {
  const mode = config.mode || "Stdio"; // Default to Stdio if not set

  // For command and fetch_stdio_tools, only show when mode is Stdio
  if (inputDef.name === "command" || inputDef.name === "fetch_stdio_tools") {
    return mode === "Stdio";
  }

  // For sse_url and fetch_sse_tools, only show when mode is SSE
  if (inputDef.name === "sse_url" || inputDef.name === "fetch_sse_tools") {
    return mode === "SSE";
  }

  // For selected_tool_name, check connection_status
  if (inputDef.name === "selected_tool_name") {
    const connectionStatus = config.connection_status || "Not Connected";
    return connectionStatus === "Connected";
  }

  // For refresh_tools and disconnect buttons, always hide them
  if (inputDef.name === "refresh_tools" || inputDef.name === "disconnect") {
    return false;
  }

  // Use the utility function for standard visibility rules
  return shouldShowInput(inputDef, config);
}
