'use client';
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';

interface AddVariableDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave?: (variable: { key: string; value: string; secure: boolean }) => void;
}

export default function AddVariableDialog({ open, onOpenChange, onSave }: AddVariableDialogProps) {
  const [key, setKey] = useState('');
  const [value, setValue] = useState('');
  const [secure, setSecure] = useState(false);

  const handleSave = () => {
    if (onSave) {
      onSave({ key, value, secure });
      setKey('');
      setValue('');
      setSecure(false);
    } else {
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[700px] w-full rounded-[20px] bg-[#232326] border-none shadow-[0_8px_32px_0_rgba(0,0,0,0.40)] p-8 font-[Satoshi]">
        <DialogHeader>
          <DialogTitle className="text-white text-[22px] font-bold mb-8 leading-tight">Add Variables</DialogTitle>
        </DialogHeader>
        <div className="mb-6">
          <label className="block text-white text-[16px] font-medium mb-2">Key</label>
          <input
            className="w-full h-12 rounded-lg bg-[#18181B] text-white font-[Satoshi] px-4 text-[16px] placeholder:text-[#A1A1AA] border border-[#393939] outline-none"
            placeholder="e.g., companyName"
            value={key}
            onChange={e => setKey(e.target.value)}
          />
        </div>
        <div className="mb-6">
          <label className="block text-white text-[16px] font-medium mb-2">Value</label>
          <textarea
            className="w-full h-20 rounded-lg bg-[#18181B] text-white font-[Satoshi] px-4 py-3 text-[16px] placeholder:text-[#A1A1AA] border border-[#393939] outline-none resize-none"
            placeholder="e.g., companyName"
            value={value}
            onChange={e => setValue(e.target.value)}
          />
        </div>
        <div className="flex items-center mb-10">
          <label className="text-white text-[16px] font-medium mr-4">Secure credentials</label>
          <button
            type="button"
            className={`w-10 h-6 flex items-center rounded-full p-1 transition-colors duration-200 ${secure ? 'bg-[#A020F0]' : 'bg-[#393939]'}`}
            onClick={() => setSecure(s => !s)}
            aria-pressed={secure}
            style={{ minWidth: 40, minHeight: 24 }}
          >
            <span
              className={`w-4 h-4 bg-white rounded-full shadow-md transform transition-transform duration-200 ${secure ? 'translate-x-4' : 'translate-x-0'}`}
              style={{ minWidth: 16, minHeight: 16 }}
            />
          </button>
        </div>
        <DialogFooter className="flex flex-row justify-center gap-6 mt-2">
          <button
            type="button"
            className="bg-[#fff] text-black font-[Satoshi] font-medium rounded-lg px-8 py-3 text-[16px] border border-[#393939] hover:bg-[#f3f3f3] transition"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </button>
          <button
            type="button"
            className="bg-[#A020F0] hover:bg-[#8B1FE0] text-white font-[Satoshi] font-medium rounded-lg px-8 py-3 text-[16px] transition"
            onClick={handleSave}
          >
            Save
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 