'use client';
import React from 'react';
import SettingsSidebar from './SettingsSidebar';
import Header from '../global-variable/Header';
import { usePathname } from 'next/navigation';

const navItems = [
  { key: 'dashboard', label: 'Dashboard', route: '/settings' },
  { key: 'global', label: 'Global variable', route: '/settings/global-variable' },
  { key: 'project', label: 'Project', route: '/settings/project' },
  { key: 'triggers', label: 'Triggers and scheduler', route: '/settings/triggers' },
  { key: 'marketplace', label: 'Explore marketplace', route: '/settings/marketplace' },
];

export default function SettingsLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const selectedLabel = navItems.find(item =>
    item.route === pathname || (item.route !== '/settings' && pathname.startsWith(item.route!))
  )?.label || 'Dashboard';
  return (
    <div className="flex w-full min-h-screen bg-[#000] dark:bg-[#000]">
      <SettingsSidebar />
      <div className="flex-1 flex flex-col">
        <Header title={selectedLabel} />
        <main className="flex-1 flex flex-col items-center justify-start">
          {children}
        </main>
      </div>
    </div>
  );
} 