'use client';
import React from 'react';

const settingsContent: Record<string, React.ReactNode> = {
  profile: <div className="p-8 text-white dark:text-white font-[<PERSON><PERSON>]">Profile settings content goes here.</div>,
  account: <div className="p-8 text-white dark:text-white font-[<PERSON><PERSON>]">Account settings content goes here.</div>,
  notifications: <div className="p-8 text-white dark:text-white font-[<PERSON><PERSON>]">Notifications settings content goes here.</div>,
  security: <div className="p-8 text-white dark:text-white font-[<PERSON><PERSON>]">Security settings content goes here.</div>,
};

export default function SettingsPage() {
  // The sidebar is now handled in the layout, so just render a placeholder or default content
  return (
    <main className="flex-1 flex flex-col items-center justify-start overflow-y-auto">
      {settingsContent.profile}
    </main>
  );
} 