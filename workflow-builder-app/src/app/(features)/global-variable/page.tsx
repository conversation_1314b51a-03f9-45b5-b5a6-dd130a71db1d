import React from 'react';
import Header from './Header';

export default function GlobalVariablePage() {
  return (
    <div className="flex flex-col min-h-screen w-full bg-black">
      <Header />
      <div className="flex flex-1 flex-col items-center justify-center w-full">
        <img src="/database.svg" alt="Database Icon" className="w-32 h-32 mb-8 opacity-60" />
        <h2 className="font-[<PERSON><PERSON>] text-white text-2xl font-bold mb-2">Add a global variable</h2>
        <p className="font-[Satoshi] text-[#A1A1AA] text-base mb-8 text-center max-w-md">
          Lorem Ipsum is simply dummy text of the printing and typesetting industry
        </p>
        <button className="bg-[#A020F0] hover:bg-[#8B1FE0] text-white font-[<PERSON><PERSON>] font-medium rounded-lg px-8 py-3 text-base flex items-center gap-2 transition">
          <span className="text-xl font-bold">+</span> Add variables
        </button>
      </div>
    </div>
  );
} 