import React from 'react';

export default function GlobalVariableHeader({ title = 'Dashboard' }: { title?: string }) {
  return (
    <header className="flex items-center justify-between w-full pl-6 pr-6 py-4 border-b border-[#393939] bg-black">
      <h1 className="font-[<PERSON><PERSON>] text-white text-lg font-bold">{title}</h1>
      <div className="flex items-center gap-4">
        <img src="/file.svg" alt="icon" className="w-6 h-6 opacity-80" />
        <img src="/file.svg" alt="icon" className="w-6 h-6 opacity-80" />
        <button className="bg-[#A020F0] hover:bg-[#8B1FE0] text-white font-[<PERSON><PERSON>] font-medium rounded-lg px-5 py-2 text-sm ml-2 transition">
          + Create new workflow
        </button>
      </div>
    </header>
  );
} 