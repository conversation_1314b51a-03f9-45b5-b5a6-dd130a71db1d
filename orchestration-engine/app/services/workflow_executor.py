# workflow_executor.py

import asyncio
import json
import uuid
from typing import Any, Dict, Optional

from aiokafka import AIOKafkaProducer, AIOKafkaConsumer  # type: ignore
from aiokafka.errors import KafkaError  # type: ignore
from app.config.config import settings
from app.utils.enhanced_logger import get_logger


logger = get_logger("WorkflowExecutor")


class WorkflowExecutionError(Exception):
    pass


class WorkflowExecutor:
    def __init__(self, producer: AIOKafkaProducer):
        self.logger = logger
        if producer is None:
            raise ValueError("A running AIOKafkaProducer instance must be provided.")
        if not getattr(producer._sender, "_running", True):
            self.logger.warning("The provided Kafka Producer may not be running.")
        self.producer = producer
        self._bootstrap_servers = settings.kafka_bootstrap_servers
        self._request_topic = settings.kafka_workflow_execution_request_topic
        self._results_topic = settings.kafka_workflow_execution_result_topic

        self._current_correlation_id: Optional[str] = None
        self._current_user_id: Optional[str] = None
        self.logger.info("WorkflowExecutor initialized.")

    def set_correlation_id(self, correlation_id: str):
        """
        Set the current correlation ID for this executor instance.
        This ID will be included in all workflow execution requests.

        Args:
            correlation_id: The correlation ID to use for subsequent requests
        """
        self._current_correlation_id = correlation_id
        self.logger.debug(f"Set correlation ID to: {correlation_id}")

    def set_user_id(self, user_id: str):
        """
        Set the current user ID for this executor instance.
        This ID will be included in all workflow execution requests.

        Args:
            user_id: The user ID to use for subsequent requests
        """
        self._current_user_id = user_id
        self.logger.debug(f"Set user ID to: {user_id}")

    async def execute_tool(
        self,
        tool_name: str = None,
        tool_parameters: dict = None,
        transition_id: str = None,
        node_label: str = None,
        input_data_configs: list = None,
        **kwargs,
    ) -> Any:
        """
        Execute a workflow based on tool_parameters.

        The tool_parameters should contain:
        - workflow_id: The ID of the target workflow to execute
        - field_mappings: Object mapping input field names to their transition IDs

        Args:
            tool_name: The name of the workflow executor tool
            tool_parameters: Parameters containing workflow_id and field_mappings
            transition_id: The transition ID from the orchestration engine
            node_label: The label of the current node
            input_data_configs: Input data configuration mappings

        Returns:
            The result from the workflow execution request
        """
        if not self.producer or not getattr(self.producer._sender, "_running", True):
            raise RuntimeError(
                "The provided Kafka Producer is not running or not available."
            )

        request_id = str(uuid.uuid4())

        # Extract workflow_id and field_mappings from tool_parameters
        workflow_id = tool_parameters.get("workflow_id")
        field_mappings = tool_parameters.get("field_mappings", {})

        if not workflow_id:
            raise ValueError("workflow_id is required in tool_parameters")

        if not field_mappings:
            raise ValueError("field_mappings is required in tool_parameters")

        # Build context info for logging
        context_info = []
        if self._current_correlation_id:
            context_info.append(f"correlation_id: {self._current_correlation_id}")
        if self._current_user_id:
            context_info.append(f"user_id: {self._current_user_id}")

        context_str = f" with {', '.join(context_info)}" if context_info else ""

        self.logger.info(
            f"Executing workflow '{workflow_id}' via Kafka (request_id: {request_id}){context_str} using provided producer."
        )

        # Build the workflow execution payload
        workflow_payload = await self._build_workflow_payload(
            workflow_id,
            field_mappings,
            tool_parameters,  # Pass resolved tool_parameters instead of input_data_configs
            request_id,
        )

        # Add correlation_id to the payload if it's set
        if self._current_correlation_id:
            workflow_payload["correlation_id"] = self._current_correlation_id
            self.logger.debug(
                f"Added correlation_id {self._current_correlation_id} to payload"
            )

        # Prepare headers for the Kafka message
        headers = [
            ("correlationId", request_id.encode("utf-8")),
            ("reply-topic", self._results_topic.encode("utf-8")),
        ]

        try:
            # Start consumer BEFORE sending the request to avoid missing responses
            result = await self._execute_workflow_with_consumer(
                workflow_payload, headers, request_id, workflow_id
            )
            return result

        except KafkaError as e:
            self.logger.error(
                f"Kafka error during workflow execution {request_id}: {e}",
                exc_info=True,
            )
            raise WorkflowExecutionError(
                f"Kafka error executing workflow request {request_id}: {e}"
            ) from e
        except Exception as e:
            self.logger.error(
                f"Error during workflow execution {request_id}: {e}", exc_info=True
            )
            raise e

    async def _build_workflow_payload(
        self,
        workflow_id: str,
        field_mappings: Dict[str, str],
        resolved_tool_parameters: dict,
        request_id: str,  # pylint: disable=unused-argument
    ) -> dict:
        """
        Build the workflow execution payload according to the specified format.

        Args:
            workflow_id: The target workflow ID
            field_mappings: Mapping of field names to transition IDs
            resolved_tool_parameters: Already resolved tool parameters with actual values
            request_id: The request ID for this execution

        Returns:
            The formatted workflow execution payload
        """
        self.logger.debug(
            f"Building workflow payload for workflow_id: {workflow_id}, field_mappings: {field_mappings}"
        )

        # Extract field values from resolved tool parameters
        field_values = self._extract_field_values_from_resolved_params(
            field_mappings, resolved_tool_parameters
        )

        # Build user_dependent_fields array (field names)
        user_dependent_fields = list(field_mappings.keys())

        # Build user_payload_template
        user_payload_template = {}
        for field_name, transition_id in field_mappings.items():
            field_value = field_values.get(field_name, "")
            user_payload_template[field_name] = {
                "transition_id": transition_id,
                "value": field_value,
            }

        # Build the final payload in the format expected by executor server
        workflow_payload = {
            "task_id": 1752221010,  # Use a consistent task_id for workflow execution
            "task_type": "workflow",
            "data": {
                "user_id": self._current_user_id,
                "workflow_id": workflow_id,
                "approval": False,  # Always false as specified
                "payload": {
                    "user_dependent_fields": user_dependent_fields,
                    "user_payload_template": user_payload_template,
                },
            },
            "approval": False,  # Also at top level
        }

        self.logger.debug(f"Built workflow payload: {workflow_payload}")
        return workflow_payload

    def _extract_field_values_from_resolved_params(
        self, field_mappings: Dict[str, str], resolved_tool_parameters: dict
    ) -> Dict[str, Any]:
        """
        Extract field values from already resolved tool parameters.

        Args:
            field_mappings: Mapping of field names to transition IDs
            resolved_tool_parameters: Already resolved tool parameters with actual values

        Returns:
            Dictionary mapping field names to their values
        """
        field_values = {}

        # Extract values for each field from the resolved parameters
        for field_name in field_mappings.keys():
            # The resolved tool parameters should contain the actual values
            field_value = resolved_tool_parameters.get(field_name, "")
            field_values[field_name] = field_value

        self.logger.debug(f"Extracted field values: {field_values}")
        return field_values

    async def _execute_workflow_with_consumer(
        self,
        workflow_payload: dict,
        headers: list,
        correlation_id: str,
        workflow_id: str,
    ) -> dict:
        """
        Execute workflow by starting consumer first, then sending request.
        This ensures we don't miss any responses.
        """
        self.logger.info(
            f"Starting consumer for workflow {workflow_id} (correlation_id: {correlation_id})"
        )

        # Create consumer with unique group ID to avoid conflicts
        temp_consumer = AIOKafkaConsumer(
            self._results_topic,
            bootstrap_servers=self._bootstrap_servers,
            group_id=f"workflow-executor-{correlation_id[:8]}",
            auto_offset_reset="latest",  # Start from latest to avoid old messages
            enable_auto_commit=True,
        )

        try:
            self.logger.info(
                f"Starting temporary consumer for workflow {workflow_id}..."
            )
            await temp_consumer.start()
            self.logger.info(
                f"✅ Successfully started consumer for workflow {workflow_id}"
            )

            # Small delay to ensure consumer is ready to receive messages
            await asyncio.sleep(0.5)

            # Now send the request
            self.logger.debug(
                f"Sending workflow request to topic '{self._request_topic}' with headers {headers}: {workflow_payload}"
            )
            await self.producer.send(
                self._request_topic, value=workflow_payload, headers=headers
            )
            self.logger.info(
                f"Sub-workflow execution request {correlation_id} sent successfully for workflow {workflow_id}."
            )

            # Now wait for completion
            responses = []
            timeout_seconds = 300  # 5 minutes timeout
            start_time = asyncio.get_event_loop().time()

            async for msg in temp_consumer:
                try:
                    response = json.loads(msg.value.decode("utf-8"))
                    self.logger.info(
                        f"🔔 WorkflowExecutor received message: Topic={msg.topic}, Offset={msg.offset}, Response={response}"
                    )

                    # Check if this response is for our correlation_id
                    if response.get("correlation_id") == correlation_id:
                        responses.append(response)
                        self.logger.debug(
                            f"Added response {len(responses)} for correlation_id {correlation_id}"
                        )

                        # Check if workflow is completed
                        if response.get("workflow_status") == "completed":
                            self.logger.info(
                                f"Workflow {workflow_id} completed. Total responses: {len(responses)}"
                            )

                            # Find the last response with actual workflow data (has raw_result or meaningful result)
                            # Exclude timing logs and completion messages
                            data_responses = []
                            for resp in responses:
                                # Skip timing log responses
                                if resp.get("status") == "time_logged":
                                    continue
                                # Skip completion responses without actual data
                                if resp.get("status") == "complete" and not resp.get(
                                    "raw_result"
                                ):
                                    continue
                                # Include responses with actual data
                                if resp.get("raw_result") or (
                                    resp.get("result")
                                    and resp.get("status") == "completed"
                                ):
                                    data_responses.append(resp)

                            self.logger.debug(
                                f"Found {len(data_responses)} data responses out of {len(responses)} total responses"
                            )

                            if data_responses:
                                # Use the last data response (most recent actual result)
                                result_response = data_responses[-1]
                                self.logger.debug(
                                    f"Using result response: {result_response}"
                                )

                                # Extract raw_result if available, otherwise use result
                                raw_result = result_response.get("raw_result")
                                if raw_result is not None:
                                    self.logger.info(
                                        f"Extracted raw_result: {raw_result}"
                                    )
                                    return raw_result
                                else:
                                    result_value = result_response.get("result")
                                    self.logger.info(
                                        f"No raw_result found, using result: {result_value}"
                                    )
                                    return result_value
                            else:
                                self.logger.warning(
                                    f"No data responses found for workflow {workflow_id}"
                                )
                                return {
                                    "execution_status": "completed",
                                    "workflow_execution_id": correlation_id,
                                    "message": f"Workflow {workflow_id} completed but no response data available",
                                }

                    # Check for timeout
                    if asyncio.get_event_loop().time() - start_time > timeout_seconds:
                        self.logger.error(
                            f"Timeout waiting for workflow {workflow_id} completion"
                        )
                        raise WorkflowExecutionError(
                            f"Timeout waiting for workflow {workflow_id} completion"
                        )

                except json.JSONDecodeError:
                    self.logger.warning(
                        f"Could not decode JSON from response: {msg.value.decode('utf-8', errors='ignore')}"
                    )
                except Exception as e:
                    self.logger.error(
                        f"Error processing response message: {e}", exc_info=True
                    )

        except Exception as e:
            self.logger.error(
                f"Error in workflow execution consumer: {e}", exc_info=True
            )
            raise WorkflowExecutionError(
                f"Consumer error for workflow {workflow_id}: {e}"
            ) from e
        finally:
            try:
                await temp_consumer.stop()
                self.logger.info(f"✅ Stopped consumer for workflow {workflow_id}")
            except Exception as e:
                self.logger.error(f"Error stopping consumer: {e}", exc_info=True)

    async def start(self):
        """Start the workflow executor (no consumer needed for fire-and-forget)."""
        self.logger.info("WorkflowExecutor started successfully.")

    async def stop(self):
        """Stop the workflow executor."""
        self.logger.info("WorkflowExecutor stopped.")

    async def __aenter__(self):
        await self.start()
        return self

    async def __aexit__(self, _exc_type, _exc_val, _exc_tb):
        await self.stop()
